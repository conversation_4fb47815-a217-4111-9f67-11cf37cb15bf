import { FlowT<PERSON>, FlowTagController } from "@/models/flow/flow-tag";
import { Button, Dropdown, Space, Tag } from "antd";
import type { TableColumnsType } from 'antd';
import React from "react";
import {
    EllipsisOutlined,
    EditOutlined,
    DeleteOutlined,
    StarOutlined,
    StarFilled,
} from '@ant-design/icons';
import DataTable from "@/components/data-table";
import TimeAgo from 'react-timeago';

interface FlowTagListProps {
    mutateObjects?: FlowTag[];
    onEdit?: (record: FlowTag) => void;
    onDelete?: (record: FlowTag) => void;
}

const FlowTagList: React.FC<FlowTagListProps> = (params) => {
    const [pendingData, setPendingData] = React.useState<string[]>([]);
    const [isLoadingData, setIsLoadingData] = React.useState<boolean>(false);

    const contextMenuProps = (record: FlowTag) => ({
        items: [
            {
                key: 'edit',
                label: 'Edit',
                icon: <EditOutlined />,
                onClick: () => {
                    if (params.onEdit) {
                        params.onEdit(record);
                    }
                }
            },
            {
                type: 'divider'
            },
            {
                key: 'delete',
                label: 'Delete',
                icon: <DeleteOutlined />,
                danger: true,
                onClick: () => {
                    if (params.onDelete) {
                        params.onDelete(record);
                    }
                }
            }
        ]
    });

    const columns: TableColumnsType<FlowTag> = [
        {
            title: '', render: (_, record) => {
                return (<Space size="small">
                    <Dropdown menu={contextMenuProps(record)}>
                        <a onClick={(e) => e.preventDefault()}>
                            <Space>
                                <Button type='text'><EllipsisOutlined rotate={90} /></Button>
                            </Space>
                        </a>
                    </Dropdown>
                </Space>)
            },
            width: 40,
        },
        {
            title: 'Flow Name', 
            dataIndex: 'flowName'
        },
        {
            title: 'Tag Name', 
            dataIndex: 'tagName',
            render: (value, record) => (
                <Space>
                    {value}
                    {record.isLatest && (
                        <Tag color="gold" icon={<StarFilled />}>
                            Latest
                        </Tag>
                    )}
                </Space>
            )
        },
        {
            title: 'Version', 
            dataIndex: 'version'
        },
        {
            title: 'Created At', 
            dataIndex: 'createdAt', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        },
        {
            title: 'Last Modified', 
            dataIndex: 'lastChangeTimestamp', 
            render: ((value, record) => {
                return <TimeAgo date={value * 1000} locale="en-US" live={true} />
            })
        }
    ];

    return (
        <DataTable<FlowTag, FlowTagController>
            controller={new FlowTagController()}
            itemUpdateInterval={10000}
            tableColumns={columns}
            onItemsValidatingChange={(isValidating: boolean, validatingIdList: string[]) => {
                setPendingData(validatingIdList);
                setIsLoadingData(isValidating);
            }}
            shouldInvalidate={(entry: FlowTag) => false}
            mutateItems={params.mutateObjects}
            rowKey="id"
        />
    )
}

export default FlowTagList;
