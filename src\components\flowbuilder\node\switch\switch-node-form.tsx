import { <PERSON><PERSON>, <PERSON>, Di<PERSON>r, Input, Space, Typography } from "antd";
import { BaseNodeFormElement } from "../base-node";
import { SwitchCondition, SwitchNodeModelData } from "./switch-node-model";
import React from "react";
import Condition from "@/components/flowbuilder/conditions/condition";
import { RuleGroupType } from "react-querybuilder";
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';

export interface SwitchNodeFormProps extends BaseNodeFormElement<SwitchNodeModelData> {
}

export default function SwitchNodeForm(props: SwitchNodeFormProps) {
    const [conditions, setConditions] = React.useState<SwitchCondition[]>(
        props.data.conditions || []
    );
    const [activeConditionIndex, setActiveConditionIndex] = React.useState<number | null>(null);

    const addCondition = () => {
        const newCondition: SwitchCondition = {
            id: crypto.randomUUID(),
            condition: { combinator: 'and', rules: [] },
            label: `Condition ${conditions.length + 1}`
        };
        setConditions([...conditions, newCondition]);
    };

    const removeCondition = (index: number) => {
        const newConditions = conditions.filter((_, i) => i !== index);
        setConditions(newConditions);
        if (activeConditionIndex === index) {
            setActiveConditionIndex(null);
        }
    };

    const updateCondition = (index: number, newCondition: RuleGroupType) => {
        const newConditions = [...conditions];
        newConditions[index] = {
            ...newConditions[index],
            condition: newCondition
        };
        setConditions(newConditions);
    };

    const updateConditionLabel = (index: number, label: string) => {
        const newConditions = [...conditions];
        newConditions[index] = {
            ...newConditions[index],
            label: label
        };
        setConditions(newConditions);
    };

    const handleSave = () => {
        props.onChange({
            conditions: conditions
        });
        props.onCancel();
    };

    // For now, using empty fields array. In a real implementation, 
    // this would be passed from the parent component
    const fields = [];

    return (
        <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Typography.Title level={4}>Switch Conditions</Typography.Title>
            <Typography.Text type="secondary">
                Define conditions for each case. Each condition will create a separate output.
                A default output will be created for cases where no condition matches.
            </Typography.Text>
            
            <Button 
                type="dashed" 
                icon={<PlusOutlined />} 
                onClick={addCondition}
                style={{ width: '100%' }}
            >
                Add Condition
            </Button>

            {conditions.map((condition, index) => (
                <Card 
                    key={condition.id}
                    title={
                        <Space>
                            <Input 
                                value={condition.label}
                                onChange={(e) => updateConditionLabel(index, e.target.value)}
                                placeholder={`Condition ${index + 1}`}
                                style={{ width: 200 }}
                            />
                        </Space>
                    }
                    extra={
                        <Button 
                            icon={<DeleteOutlined />} 
                            danger 
                            size="small"
                            onClick={() => removeCondition(index)}
                        />
                    }
                >
                    <Condition 
                        fields={fields} 
                        query={condition.condition} 
                        onChange={(newCondition) => updateCondition(index, newCondition)}
                        onCancel={() => {}}
                    />
                </Card>
            ))}
            
            <Divider />
            <Space>
                <Button type="primary" onClick={handleSave}>
                    Save
                </Button>
                <Button onClick={props.onCancel}>
                    Cancel
                </Button>
            </Space>
        </Space>
    );
}
